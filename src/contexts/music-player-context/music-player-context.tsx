"use client"

import { createContext, useContext, useState, useEffect, type ReactNode, useCallback } from "react"

export interface Song {
  id: string
  title: string
  artist: string
  album: string
  albumArt: string
  duration: number // in seconds
  audioSrc: string
  credits?: {
    producer?: string
    writer?: string
    engineer?: string
  }
}

interface MusicPlayerContextType {
  currentSong: Song | null
  isPlaying: boolean
  isLoading: boolean
  volume: number
  progress: number
  duration: number
  isExpanded: boolean
  playSong: (song: Song) => void
  togglePlay: () => void
  setVolume: (volume: number) => void
  seekTo: (time: number) => void
  nextSong: () => void
  prevSong: () => void
  toggleExpanded: () => void
}

const MusicPlayerContext = createContext<MusicPlayerContextType | undefined>(undefined)

// Sample songs data
const sampleSongs: Song[] = [
  {
    id: "1",
    title: "Electric Dreams",
    artist: "John Doe",
    album: "Midnight Echoes",
    albumArt: "/placeholder.svg?height=300&width=300",
    duration: 259, // 4:19
    audioSrc: "https://sample-playlist.s3.us-east-2.amazonaws.com/playsample.wav?response-content-disposition=inline&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Security-Token=IQoJb3JpZ2luX2VjEOb%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEaCXVzLWVhc3QtMiJHMEUCIQD8Cn4MDm0DLXqTxUyRpLPSL5xCqx4g9RaYB2eww6uHmAIgSw7cn8J8i1sgIWuv3qr15OwgaT7GqmhrV7%2Fa2FqmQTkqsAQI%2F%2F%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FARAAGgw0MDI5NjEzOTgxNTgiDIlkdN1QJqRyF0YY1iqEBJIbpQUiEWC15tK5aZCtffttuzABvmMLp%2FOmXRngoZBEQ92pK7HVMkXHB7vcknviiz5Y6ZilJy7aXfv50Bev1FUJk5qVrT5z2M1hfkKRgkfyroVZpEpWd4zCdNL7Ln0pKWP%2B1iyo6wf8mU%2BeKOeT5gklOwGqaqVOPQHRq3qXWaTQggMm%2FaDaoKYW97dpiBa1dhXkrHcClAFFNDGumCnfPXphRuowhEju45IMFGSeABfu6t1bKq8VfstA03bDpgd5T2zgv%2B5xah1Wx%2B1OyfRHHPY1LpgKom8cW69fNqPF%2FMdt4B7lHvgq6T%2BkIfWci661KZOMPAyUxyhVzC4Aeo8rRIawzSsyCCUlPHnLzFgdldnSfjeSwxslow5dqyMwmleFHG%2FIp5DGt9yZ7Bcp2%2BkoJdBctV%2BxmFHKDMeDepyA0tlJIzaLIObqSABw9iepBsSArjVvo5rIYt5BbwP5ivV9c4jBoRcJWvubdnRnMO8NN3wxmYqBgck7pyWhf82%2Bmu3VJkNE4cxabGTLZL4d%2FXWtxsmfLHpcyQHAUhv%2FUXMrCH4mQh4X4xKAlmZbcyalXDNhRMYzKWnTDuTc1vGJeZhFXBctfqOXJzqjDhbv6XDhN42zqbJI9wAcO4CrFk2rxUQFgIt%2FuCuUmk565XbimcFVL170qpHvaEXNnrWrPY9Npi8UEW%2Fs4zCw5IHEBjrFAkKpbjvySi7gUO%2Bae5AMUWtwX1S93NXYNtvF4ua%2FcHyKiUYhR10bJ%2BN323DuONzov1zHYuqNdpaN0qftUJSQNhDJi32QfOEiqx8USCRHb2UECLm%2F3%2Fn2jSUVYNONppW6C3SoM%2B9lKcoh0qmfHxdKZbA8RqEmF1a5kB7t1Ph7pilyaZT67vwcjUE7%2Fken4EZfD65ZSRT%2FGFxFXUp6Lbc4kBCwKZsCtCwPheFv6ptO85jN%2BKybT8fze6zw8UW0a7nlztzZS1vMFPVjG2ISwPbK2dunab6UBwK5xzNSwcE1IlYh5rFNszAbypHKl7Le8I6l2D4wSacxh06nPF51HMRlNLJhqGNhQoXutUN4TL%2FXMghvgYodhRIqezmZhLdx2IznJ4wqVE%2FGmC5TlS4vXJMsjDy%2FVN1vbYuOABAHAHswGi1q4febp%2BA%3D&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=ASIAV3US67GHNR4ESHTO%2F20250723%2Fus-east-2%2Fs3%2Faws4_request&X-Amz-Date=20250723T054211Z&X-Amz-Expires=43200&X-Amz-SignedHeaders=host&X-Amz-Signature=010e566a80ce7e00cb8037c9012fb26f1764ac72e184029c5f04c45a09331219",
    credits: {
      producer: "John Doe",
      writer: "John Doe, Sarah Mitchell",
      engineer: "Alex Thompson",
    },
  },
  {
    id: "2",
    title: "Neon Nights",
    artist: "Sarah Mitchell",
    album: "Urban Landscapes",
    albumArt: "/placeholder.svg?height=300&width=300",
    duration: 222, // 3:42
    audioSrc: "https://sample-playlist.s3.us-east-2.amazonaws.com/playsample.wav?response-content-disposition=inline&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Security-Token=IQoJb3JpZ2luX2VjEOb%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEaCXVzLWVhc3QtMiJHMEUCIQD8Cn4MDm0DLXqTxUyRpLPSL5xCqx4g9RaYB2eww6uHmAIgSw7cn8J8i1sgIWuv3qr15OwgaT7GqmhrV7%2Fa2FqmQTkqsAQI%2F%2F%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FARAAGgw0MDI5NjEzOTgxNTgiDIlkdN1QJqRyF0YY1iqEBJIbpQUiEWC15tK5aZCtffttuzABvmMLp%2FOmXRngoZBEQ92pK7HVMkXHB7vcknviiz5Y6ZilJy7aXfv50Bev1FUJk5qVrT5z2M1hfkKRgkfyroVZpEpWd4zCdNL7Ln0pKWP%2B1iyo6wf8mU%2BeKOeT5gklOwGqaqVOPQHRq3qXWaTQggMm%2FaDaoKYW97dpiBa1dhXkrHcClAFFNDGumCnfPXphRuowhEju45IMFGSeABfu6t1bKq8VfstA03bDpgd5T2zgv%2B5xah1Wx%2B1OyfRHHPY1LpgKom8cW69fNqPF%2FMdt4B7lHvgq6T%2BkIfWci661KZOMPAyUxyhVzC4Aeo8rRIawzSsyCCUlPHnLzFgdldnSfjeSwxslow5dqyMwmleFHG%2FIp5DGt9yZ7Bcp2%2BkoJdBctV%2BxmFHKDMeDepyA0tlJIzaLIObqSABw9iepBsSArjVvo5rIYt5BbwP5ivV9c4jBoRcJWvubdnRnMO8NN3wxmYqBgck7pyWhf82%2Bmu3VJkNE4cxabGTLZL4d%2FXWtxsmfLHpcyQHAUhv%2FUXMrCH4mQh4X4xKAlmZbcyalXDNhRMYzKWnTDuTc1vGJeZhFXBctfqOXJzqjDhbv6XDhN42zqbJI9wAcO4CrFk2rxUQFgIt%2FuCuUmk565XbimcFVL170qpHvaEXNnrWrPY9Npi8UEW%2Fs4zCw5IHEBjrFAkKpbjvySi7gUO%2Bae5AMUWtwX1S93NXYNtvF4ua%2FcHyKiUYhR10bJ%2BN323DuONzov1zHYuqNdpaN0qftUJSQNhDJi32QfOEiqx8USCRHb2UECLm%2F3%2Fn2jSUVYNONppW6C3SoM%2B9lKcoh0qmfHxdKZbA8RqEmF1a5kB7t1Ph7pilyaZT67vwcjUE7%2Fken4EZfD65ZSRT%2FGFxFXUp6Lbc4kBCwKZsCtCwPheFv6ptO85jN%2BKybT8fze6zw8UW0a7nlztzZS1vMFPVjG2ISwPbK2dunab6UBwK5xzNSwcE1IlYh5rFNszAbypHKl7Le8I6l2D4wSacxh06nPF51HMRlNLJhqGNhQoXutUN4TL%2FXMghvgYodhRIqezmZhLdx2IznJ4wqVE%2FGmC5TlS4vXJMsjDy%2FVN1vbYuOABAHAHswGi1q4febp%2BA%3D&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=ASIAV3US67GHNR4ESHTO%2F20250723%2Fus-east-2%2Fs3%2Faws4_request&X-Amz-Date=20250723T054211Z&X-Amz-Expires=43200&X-Amz-SignedHeaders=host&X-Amz-Signature=010e566a80ce7e00cb8037c9012fb26f1764ac72e184029c5f04c45a09331219",
    credits: {
      producer: "Sarah Mitchell",
      writer: "Sarah Mitchell",
      engineer: "Emma Wilson",
    },
  },
  {
    id: "3",
    title: "City Lights",
    artist: "The Bridges",
    album: "Metropolitan",
    albumArt: "/placeholder.svg?height=300&width=300",
    duration: 315, // 5:15
    audioSrc: "https://sample-playlist.s3.us-east-2.amazonaws.com/playsample.wav?response-content-disposition=inline&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Security-Token=IQoJb3JpZ2luX2VjEOb%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEaCXVzLWVhc3QtMiJHMEUCIQD8Cn4MDm0DLXqTxUyRpLPSL5xCqx4g9RaYB2eww6uHmAIgSw7cn8J8i1sgIWuv3qr15OwgaT7GqmhrV7%2Fa2FqmQTkqsAQI%2F%2F%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FARAAGgw0MDI5NjEzOTgxNTgiDIlkdN1QJqRyF0YY1iqEBJIbpQUiEWC15tK5aZCtffttuzABvmMLp%2FOmXRngoZBEQ92pK7HVMkXHB7vcknviiz5Y6ZilJy7aXfv50Bev1FUJk5qVrT5z2M1hfkKRgkfyroVZpEpWd4zCdNL7Ln0pKWP%2B1iyo6wf8mU%2BeKOeT5gklOwGqaqVOPQHRq3qXWaTQggMm%2FaDaoKYW97dpiBa1dhXkrHcClAFFNDGumCnfPXphRuowhEju45IMFGSeABfu6t1bKq8VfstA03bDpgd5T2zgv%2B5xah1Wx%2B1OyfRHHPY1LpgKom8cW69fNqPF%2FMdt4B7lHvgq6T%2BkIfWci661KZOMPAyUxyhVzC4Aeo8rRIawzSsyCCUlPHnLzFgdldnSfjeSwxslow5dqyMwmleFHG%2FIp5DGt9yZ7Bcp2%2BkoJdBctV%2BxmFHKDMeDepyA0tlJIzaLIObqSABw9iepBsSArjVvo5rIYt5BbwP5ivV9c4jBoRcJWvubdnRnMO8NN3wxmYqBgck7pyWhf82%2Bmu3VJkNE4cxabGTLZL4d%2FXWtxsmfLHpcyQHAUhv%2FUXMrCH4mQh4X4xKAlmZbcyalXDNhRMYzKWnTDuTc1vGJeZhFXBctfqOXJzqjDhbv6XDhN42zqbJI9wAcO4CrFk2rxUQFgIt%2FuCuUmk565XbimcFVL170qpHvaEXNnrWrPY9Npi8UEW%2Fs4zCw5IHEBjrFAkKpbjvySi7gUO%2Bae5AMUWtwX1S93NXYNtvF4ua%2FcHyKiUYhR10bJ%2BN323DuONzov1zHYuqNdpaN0qftUJSQNhDJi32QfOEiqx8USCRHb2UECLm%2F3%2Fn2jSUVYNONppW6C3SoM%2B9lKcoh0qmfHxdKZbA8RqEmF1a5kB7t1Ph7pilyaZT67vwcjUE7%2Fken4EZfD65ZSRT%2FGFxFXUp6Lbc4kBCwKZsCtCwPheFv6ptO85jN%2BKybT8fze6zw8UW0a7nlztzZS1vMFPVjG2ISwPbK2dunab6UBwK5xzNSwcE1IlYh5rFNszAbypHKl7Le8I6l2D4wSacxh06nPF51HMRlNLJhqGNhQoXutUN4TL%2FXMghvgYodhRIqezmZhLdx2IznJ4wqVE%2FGmC5TlS4vXJMsjDy%2FVN1vbYuOABAHAHswGi1q4febp%2BA%3D&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=ASIAV3US67GHNR4ESHTO%2F20250723%2Fus-east-2%2Fs3%2Faws4_request&X-Amz-Date=20250723T054211Z&X-Amz-Expires=43200&X-Amz-SignedHeaders=host&X-Amz-Signature=010e566a80ce7e00cb8037c9012fb26f1764ac72e184029c5f04c45a09331219",
    credits: {
      producer: "Mike Rodriguez",
      writer: "The Bridges",
      engineer: "Alex Thompson",
    },
  },
]

export const MusicPlayerProvider = ({ children }: { children: ReactNode }) => {
  const [currentSong, setCurrentSong] = useState<Song | null>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [volume, setVolume] = useState(0.7)
  const [progress, setProgress] = useState(0)
  const [duration, setDuration] = useState(0)
  const [isExpanded, setIsExpanded] = useState(false)
  const [audioElement, setAudioElement] = useState<HTMLAudioElement | null>(null)
  const [currentSongIndex, setCurrentSongIndex] = useState(0)

  // Initialize audio element
  useEffect(() => {
    const audio = new Audio()
    setAudioElement(audio)

    return () => {
      audio.pause()
      audio.src = ""
    }
  }, [])

  // Play a specific song
  const playSong = useCallback((song: Song) => {
    const songIndex = sampleSongs.findIndex((s) => s.id === song.id)
    if (songIndex !== -1) {
      setCurrentSongIndex(songIndex)
    }
    setCurrentSong(song)
    setIsPlaying(true)
  }, []);

  // Play next song
  const nextSong = useCallback(() => {
    if (sampleSongs.length === 0) return

    const nextIndex = (currentSongIndex + 1) % sampleSongs.length
    setCurrentSongIndex(nextIndex)
    playSong(sampleSongs[nextIndex])
  }, [currentSongIndex, playSong])

  // Set up audio element event listeners (only when audio element or current song changes)
  useEffect(() => {
    if (audioElement && currentSong) {
      // Only set src if it's different to avoid restarting
      if (audioElement.src !== currentSong.audioSrc) {
        audioElement.src = currentSong.audioSrc
      }
      audioElement.volume = volume

      // Set up event listeners for audio metadata
      const handleLoadStart = () => {
        setIsLoading(true)
      }

      const handleLoadedMetadata = () => {
        setDuration(audioElement.duration)
        setIsLoading(false)
      }

      const handleTimeUpdate = () => {
        setProgress(audioElement.currentTime)
      }

      const handlePlay = () => {
        setIsPlaying(true)
      }

      const handlePause = () => {
        setIsPlaying(false)
      }

      const handleEnded = () => {
        setIsPlaying(false)
        if (sampleSongs.length === 0) return
        const nextIndex = (currentSongIndex + 1) % sampleSongs.length
        setCurrentSongIndex(nextIndex)
        playSong(sampleSongs[nextIndex])
      }

      // Add event listeners
      audioElement.addEventListener('loadstart', handleLoadStart)
      audioElement.addEventListener('loadedmetadata', handleLoadedMetadata)
      audioElement.addEventListener('timeupdate', handleTimeUpdate)
      audioElement.addEventListener('play', handlePlay)
      audioElement.addEventListener('pause', handlePause)
      audioElement.addEventListener('ended', handleEnded)

      // Cleanup function
      return () => {
        audioElement.removeEventListener('loadstart', handleLoadStart)
        audioElement.removeEventListener('loadedmetadata', handleLoadedMetadata)
        audioElement.removeEventListener('timeupdate', handleTimeUpdate)
        audioElement.removeEventListener('play', handlePlay)
        audioElement.removeEventListener('pause', handlePause)
        audioElement.removeEventListener('ended', handleEnded)
      }
    }
  }, [audioElement, currentSong, volume, currentSongIndex, playSong])

  // Handle play/pause state changes separately
  useEffect(() => {
    if (audioElement && currentSong) {
      if (isPlaying) {
        audioElement.play().catch((error) => {
          console.error("Error playing audio:", error)
          setIsPlaying(false)
        })
      } else {
        audioElement.pause()
      }
    }
  }, [audioElement, currentSong, isPlaying])

  // Toggle play/pause
  const togglePlay = useCallback(() => {
    if (!currentSong) {
      // If no song is selected, play the first one
      if (sampleSongs.length > 0) {
        playSong(sampleSongs[0])
      }
      return
    }

    if (audioElement) {
      if (isPlaying) {
        audioElement.pause()
      } else {
        audioElement.play().catch((error) => {
          console.error("Error playing audio:", error)
          setIsPlaying(false)
        })
      }
    }
  }, [currentSong, audioElement, isPlaying, playSong])

  // Set volume
  const setVolumeHandler = useCallback((newVolume: number) => {
    const clampedVolume = Math.max(0, Math.min(1, newVolume))
    if (audioElement) {
      audioElement.volume = clampedVolume
    }
    setVolume(clampedVolume)
  }, [audioElement])

  // Seek to a specific time
  const seekTo = useCallback((time: number) => {
    if (audioElement && !isNaN(time) && time >= 0) {
      audioElement.currentTime = Math.min(time, audioElement.duration || 0)
      setProgress(audioElement.currentTime)
    }
  }, [audioElement])

  // Play previous song
  const prevSong = useCallback(() => {
    if (sampleSongs.length === 0) return

    const prevIndex = (currentSongIndex - 1 + sampleSongs.length) % sampleSongs.length
    setCurrentSongIndex(prevIndex)
    playSong(sampleSongs[prevIndex])
  }, [currentSongIndex, playSong])

  // Toggle expanded view
  const toggleExpanded = () => {
    setIsExpanded(!isExpanded)
  }

  const value = {
    currentSong,
    isPlaying,
    isLoading,
    volume,
    progress,
    duration,
    isExpanded,
    playSong,
    togglePlay,
    setVolume: setVolumeHandler,
    seekTo,
    nextSong,
    prevSong,
    toggleExpanded,
  }

  return <MusicPlayerContext.Provider value={value}>{children}</MusicPlayerContext.Provider>
}

export const useMusicPlayer = () => {
  const context = useContext(MusicPlayerContext)
  if (context === undefined) {
    throw new Error("useMusicPlayer must be used within a MusicPlayerProvider")
  }
  return context
}
