import { Amplify } from 'aws-amplify';
import type { ResourcesConfig } from 'aws-amplify';

export const cognitoConfig = {
  region: process.env.NEXT_PUBLIC_COGNITO_REGION!,
  userPoolId: process.env.NEXT_PUBLIC_COGNITO_USER_POOL_ID!,
  clientId: process.env.NEXT_PUBLIC_COGNITO_CLIENT_ID!,
  identityPoolId: process.env.NEXT_PUBLIC_COGNITO_IDENTITY_POOL_ID,
};

// Initialize AWS Amplify with Cognito configuration
export function initCognitoAuth() {
  const amplifyConfig: ResourcesConfig = {
    Auth: {
      Cognito: {
        userPoolId: cognitoConfig.userPoolId,
        userPoolClientId: cognitoConfig.clientId,
        loginWith: {
          email: true,
          username: false
        }
        // Removed identityPoolId to avoid conflicts with API key auth
      }
    },
    API: {
      GraphQL: {
        endpoint: 'https://2xymbka4zzam3b347bgugpfp6a.appsync-api.us-east-2.amazonaws.com/graphql',
        region: 'us-east-2',
        defaultAuthMode: 'apiKey' as const,
        apiKey: 'da2-hu56cxlaxreutk6qxxkxhab7ly'
      }
    }
  };

  // Configure region separately or through environment
  process.env.AWS_REGION = cognitoConfig.region;

  Amplify.configure(amplifyConfig);
}