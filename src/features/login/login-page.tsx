"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Button } from "@/components/ui/button"
import { LoginForm } from "@/components/auth/login-form"
import { EmailVerificationForm } from "@/components/auth/email-verification-form"
import { useRouter, useSearchParams } from "next/navigation"
import { useAuth } from "@/contexts/auth/auth-context"
import { useEffect, useState } from "react"
import Link from "next/link"
import type { AuthUser } from "@/lib/auth-utils"
import Image from "next/image"

type LoginStep = "login" | "verification"

export default function LoginPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { refreshUser } = useAuth()
  const [showSuccessMessage, setShowSuccessMessage] = useState(false)
  const [currentStep, setCurrentStep] = useState<LoginStep>("login")
  const [verificationEmail, setVerificationEmail] = useState("")

  // Check for verification success message
  useEffect(() => {
    if (searchParams.get('message') === 'verification-success') {
      setShowSuccessMessage(true)
      // Clear the message after 5 seconds
      setTimeout(() => setShowSuccessMessage(false), 5000)
    }
  }, [searchParams])

  // Note: Auth redirects handled automatically by AuthWrapper

  const handleLoginSuccess = async (user: AuthUser) => {
    console.log('Login successful:', user)
    await refreshUser()

    // Small delay to ensure auth state is properly updated
    setTimeout(() => {
      // Check onboarding status and redirect accordingly
      try {
        const onboardingStatus = localStorage.getItem("onboarding");

        if (onboardingStatus === "done") {
          router.push('/');
        } else {
          router.push('/onboarding');
        }
      } catch (error) {
        console.error("Error checking onboarding status:", error);
        // Default to onboarding if there's an error
        router.push('/onboarding');
      }
    }, 100);
  }

  const handleLoginError = (error: string) => {
    console.error('Login error:', error)
  }

  const handleVerificationRequired = (email: string) => {
    setVerificationEmail(email)
    setCurrentStep("verification")
    // Show a brief success message that code was sent
    setShowSuccessMessage(true)
    setTimeout(() => setShowSuccessMessage(false), 5000)
  }

  const handleVerificationSuccess = async () => {
    // After verification, go back to login
    setCurrentStep("login")
    setVerificationEmail("")
    setShowSuccessMessage(true)
    // Clear the message after 5 seconds
    setTimeout(() => setShowSuccessMessage(false), 5000)
  }

  const handleVerificationError = (error: string) => {
    console.error('Verification error:', error)
  }

  const handleBackToLogin = () => {
    setCurrentStep("login")
    setVerificationEmail("")
  }

  return (
    <div className="w-full max-w-md space-y-6">
      <Card>
        <CardHeader className="text-center space-y-4">
          <div className=" flex items-center justify-center mb-0">
                   <Image src="/SMASH-(full)-logo.png" width={180} height={180} alt="logo" />
   
          </div>
          <div>
          {currentStep !== "login"  &&   <CardTitle className="text-2xl">
            Verify Your Email
            </CardTitle>}
            <CardDescription className="mt-2">
              {currentStep === "login"
                ? "Please sign in to continue"
                : "Complete your sign in by verifying your email"
              }
            </CardDescription>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {showSuccessMessage && (
            <div className="bg-green-50 border border-green-200 text-green-800 text-sm p-3 rounded-md">
              {currentStep === "verification"
                ? "📧 Verification code sent! Please check your email."
                : "✅ Email verified successfully! You can now sign in with your credentials."
              }
            </div>
          )}

          {currentStep === "login" ? (
            <>
              <LoginForm
                onSuccess={handleLoginSuccess}
                onError={handleLoginError}
                onVerificationRequired={handleVerificationRequired}
              />

              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <Separator className="w-full" />
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-background px-2 text-muted-foreground">Or</span>
                </div>
              </div>

              <Button
                asChild
                variant="outline"
                className="w-full h-12 text-base"
                size="lg"
              >
                <Link href="/signup">Create an account</Link>
              </Button>
            </>
          ) : (
            <EmailVerificationForm
              email={verificationEmail}
              onSuccess={handleVerificationSuccess}
              onError={handleVerificationError}
              onBack={handleBackToLogin}
            />
          )}
        </CardContent>
      </Card>

      {/* Features Section */}
      <Card className="bg-muted/50">
        <CardContent className="pt-6">
          <div className="space-y-4">
            <h3 className="font-semibold text-center">Why join Smash Music?</h3>
            <div className="grid gap-3 text-sm">
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-primary rounded-full" />
                <span>Discover new artists and music</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-primary rounded-full" />
                <span>Create and share playlists</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-primary rounded-full" />
                <span>Connect with your favorite artists</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-primary rounded-full" />
                <span>Access exclusive content</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
