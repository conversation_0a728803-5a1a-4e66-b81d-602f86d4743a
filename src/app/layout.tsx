import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/contexts/theme/theme-context";
import { I18nProvider } from "@/contexts/i18n/i18n-context";
import { MusicPlayerProvider } from "@/contexts/music-player-context/music-player-context"
import Header from "@/components/shared/header";
import { defaultLocale } from "@/i18n/config";
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/shared/side-bar/app-sidebar";
import AppGate from "@/components/auth/app-gate";
import { AuthProvider } from "@/contexts/auth/auth-context";
import Providers from "./providers";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Smash",
  description: "A modern music application with dark/light theme support",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // For static export, we use the default locale
  // Client-side locale switching will be handled by the LanguageToggle component
  const locale = defaultLocale;

  return (
    <html lang={locale}>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <Providers>
          <ThemeProvider>
            <SidebarProvider>
              <AuthProvider>
                <AppGate>
                  <div className="flex h-screen w-full overflow-hidden">
                    <AppSidebar />
                    <div className="flex-1 min-h-0 flex flex-col overflow-hidden">
                      <SidebarInset className="flex flex-col h-full overflow-hidden">
                        <I18nProvider>
                          <MusicPlayerProvider>
                            <Header />
                            <div className="flex-1 min-h-0 overflow-auto">
                              {children}
                            </div>
                          </MusicPlayerProvider>
                        </I18nProvider>
                      </SidebarInset>
                    </div>
                  </div>
                </AppGate>
              </AuthProvider>
            </SidebarProvider>
          </ThemeProvider>
        </Providers>
      </body>
    </html>
  );
}
